import React from "react";
import { MarkdownHooks } from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehype<PERSON>ermaid from "rehype-mermaid";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

interface TextBlockProps {
  content: string;
}

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;

  return (
    <div className="cscs-agent-text-block ag:break-all ag:leading-[1.6em]">
      <MarkdownHooks rehypePlugins={[rehypeHighlight, rehypeRaw, rehypeMermaid]} remarkPlugins={[remarkGfm]}>
        {content}
      </MarkdownHooks>
    </div>
  );
};

export default TextBlock;
